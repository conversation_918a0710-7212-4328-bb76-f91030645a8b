<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <title>费用业务运费拆账</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link rel="stylesheet" href="https://unpkg.com/layui@2.8.18/dist/css/layui.css">
  <style>
    .container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .section {
      margin-bottom: 30px;
      border: 1px solid #e6e6e6;
      border-radius: 5px;
      padding: 20px;
    }

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }

    .info-row {
      display: flex;
      margin-bottom: 10px;
    }

    .info-item {
      flex: 1;
      margin-right: 20px;
    }

    .info-item:last-child {
      margin-right: 0;
    }

    .info-label {
      display: inline-block;
      width: 100px;
      color: #666;
    }

    .info-value {
      color: #333;
    }

    .red-text {
      color: #ff0000;
    }

    .action-buttons {
      text-align: center;
      margin-top: 20px;
    }

    .action-buttons .layui-btn {
      margin: 0 10px;
    }

    .split-table {
      margin-top: 15px;
    }

    .add-row {
      color: #1E9FFF;
      cursor: pointer;
      margin-top: 10px;
    }

    .add-row:hover {
      text-decoration: underline;
    }

    .dialog-section {
      margin-bottom: 20px;
    }

    .readonly-text {
      display: inline-block;
    }

    .editable-input,
    .editable-select {
      width: 100%;
    }

    /* 弹窗内容样式 */
    #splitDialog {
      padding: 20px !important;
    }

    /* 确保弹窗内的表格有适当间距 */
    #splitDialog .layui-table {
      margin: 15px 0;
    }

    /* 弹窗内信息行的间距 */
    #splitDialog .info-row {
      margin-bottom: 12px;
    }

    /* 审核区域的样式 */
    #auditSection {
      border-top: 1px solid #e6e6e6;
      padding-top: 15px;
    }
  </style>
</head>

<body>
  <div class="container">
    <!-- 基本信息 -->
    <div class="section">
      <div class="section-title">基本信息</div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">NC客商名称：</span>
          <span class="info-value">中国邮政集团有限公司福州市分公司</span>
        </div>
        <div class="info-item">
          <span class="info-label">NC编号：</span>
          <span class="info-value">N001616</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">业务月份：</span>
          <span class="info-value">202503</span>
        </div>
        <div class="info-item">
          <span class="info-label">产品类型：</span>
          <span class="info-value">普货件</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">对应业务大类：</span>
          <span class="info-value">手续费补贴</span>
        </div>
        <div class="info-item">
          <span class="info-label">业务类型：</span>
          <span class="info-value">手续费补贴</span>
        </div>
      </div>
    </div>

    <!-- 金额信息 -->
    <div class="section">
      <div class="section-title">金额信息</div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">业务金额：</span>
          <span class="info-value">10000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">调整金额：</span>
          <span class="info-value">-1000.00</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">扣印花金额：</span>
          <span class="info-value">1000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">已开票金额：</span>
          <span class="info-value">1000.00</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">已回款金额：</span>
          <span class="info-value">1000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">可拆账金额：</span>
          <span class="info-value red-text">7000.00</span>
        </div>
      </div>

    </div>
  </div>

  <!-- 操作按钮 -->
  <div class="action-buttons">
    <button type="button" class="layui-btn layui-btn-normal" id="splitApply">拆账申请</button>
    <button type="button" class="layui-btn layui-btn-warm" id="splitAudit">拆账审核</button>
    <button type="button" class="layui-btn" id="reSplit">重新拆账</button>
  </div>
  </div>

  <!-- 拆账申请弹窗 -->
  <div id="applyDialog" style="display: none;">
    <div style="padding: 20px;">
      <!-- 基本信息区域 -->
      <div class="dialog-section">
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">NC客商名称：</span>
            <span class="info-value">中国邮政集团有限公司福州市分公司</span>
          </div>
          <div class="info-item">
            <span class="info-label">NC编号：</span>
            <span class="info-value">N001616</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">业务月份：</span>
            <span class="info-value">202503</span>
          </div>
          <div class="info-item">
            <span class="info-label">产品类型：</span>
            <span class="info-value">普货件</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">对应业务大类：</span>
            <span class="info-value">手续费补贴</span>
          </div>
          <div class="info-item">
            <span class="info-label">业务类型：</span>
            <span class="info-value">手续费补贴</span>
          </div>
        </div>
      </div>

      <!-- 金额信息区域 -->
      <div class="dialog-section">
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">业务金额：</span>
            <span class="info-value">10000.00</span>
          </div>
          <div class="info-item">
            <span class="info-label">调整金额：</span>
            <span class="info-value">-1000.00</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">扣印花金额：</span>
            <span class="info-value">1000.00</span>
          </div>
          <div class="info-item">
            <span class="info-label">已开票金额：</span>
            <span class="info-value">1000.00</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">已回款金额：</span>
            <span class="info-value">1000.00</span>
          </div>
          <div class="info-item">
            <span class="info-label">可拆账金额：</span>
            <span class="info-value red-text">7000.00</span>
          </div>
        </div>

        <!-- 拆账明细区域 -->
        <div class="dialog-section">
          <div style="margin-bottom: 10px; font-weight: bold;">拆账明细</div>
          <div class="info-row">
            <div class="info-item">
              <span class="info-label">拆账金额：</span>
              <span class="info-value">7000.00；</span>
            </div>
            <div class="info-item">
              <span class="info-label">拆账金额：</span>
              <span class="info-value">3000.00</span>
            </div>
          </div>

          <table class="layui-table" style="margin-top: 15px;">
            <thead>
              <tr>
                <th style="width: 80px;">序号</th>
                <th>NC客商名称</th>
                <th style="width: 120px;">NC编号</th>
                <th style="width: 120px;">拆账金额</th>
                <th style="width: 80px;">操作</th>
              </tr>
            </thead>
            <tbody id="applyTableBody">
              <tr>
                <td>1</td>
                <td>
                  <select class="layui-select" name="ncName1" lay-filter="ncName" lay-search data-row="1">
                    <option value="">请选择或输入搜索NC客商</option>
                  </select>
                </td>
                <td>
                  <input type="text" class="layui-input" name="ncCode1" readonly placeholder="NC编号">
                </td>
                <td>
                  <input type="text" class="layui-input" name="amount1" placeholder="请输入拆账金额">
                </td>
                <td>
                  <button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete-row">删除</button>
                </td>
              </tr>
            </tbody>
          </table>

          <div class="add-row" style="margin-top: 10px;">
            <a href="javascript:;" style="color: #1E9FFF;">+新增</a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 拆账审核弹窗 -->
  <div id="auditDialog" style="display: none;">
    <div style="padding: 20px;">
      <!-- 基本信息区域 -->
      <div class="dialog-section">
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">NC客商名称：</span>
            <span class="info-value">中国邮政集团有限公司福州市分公司</span>
          </div>
          <div class="info-item">
            <span class="info-label">NC编号：</span>
            <span class="info-value">N001616</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">业务月份：</span>
            <span class="info-value">202503</span>
          </div>
          <div class="info-item">
            <span class="info-label">产品类型：</span>
            <span class="info-value">普货件</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">对应业务大类：</span>
            <span class="info-value">手续费补贴</span>
          </div>
          <div class="info-item">
            <span class="info-label">业务类型：</span>
            <span class="info-value">手续费补贴</span>
          </div>
        </div>
      </div>

      <!-- 金额信息区域 -->
      <div class="dialog-section">
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">业务金额：</span>
            <span class="info-value">10000.00</span>
          </div>
          <div class="info-item">
            <span class="info-label">调整金额：</span>
            <span class="info-value">-1000.00</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">扣印花金额：</span>
            <span class="info-value">1000.00</span>
          </div>
          <div class="info-item">
            <span class="info-label">已开票金额：</span>
            <span class="info-value">1000.00</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">已回款金额：</span>
            <span class="info-value">1000.00</span>
          </div>
          <div class="info-item">
            <span class="info-label">可拆账金额：</span>
            <span class="info-value red-text">7000.00</span>
          </div>
        </div>

        <!-- 拆账明细区域 -->
        <div class="dialog-section">
          <div style="margin-bottom: 10px; font-weight: bold;">拆账明细</div>
          <div class="info-row">
            <div class="info-item">
              <span class="info-label">拆账金额：</span>
              <span class="info-value">7000.00；</span>
            </div>
            <div class="info-item">
              <span class="info-label">拆账金额：</span>
              <span class="info-value">3000.00</span>
            </div>
          </div>

          <table class="layui-table" style="margin-top: 15px;">
            <thead>
              <tr>
                <th style="width: 80px;">序号</th>
                <th>NC客商名称</th>
                <th style="width: 120px;">NC编号</th>
                <th style="width: 120px;">拆账金额</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>1</td>
                <td>中国邮政集团有限公司三明分公司</td>
                <td>N1224</td>
                <td>2000</td>
              </tr>
              <tr>
                <td>2</td>
                <td>中国邮政集团有限公司三明分公司</td>
                <td>N1225</td>
                <td>5000</td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 审核区域 -->
        <div class="dialog-section" style="border-top: 1px solid #e6e6e6; padding-top: 15px;">
          <div style="margin-bottom: 15px;">
            <span class="info-label">审核结果：</span>
            <select name="auditResult" lay-filter="auditResult" style="width: 200px;">
              <option value="">请选择审核结果</option>
              <option value="pass">审核通过</option>
              <option value="reject">审核拒绝</option>
            </select>
          </div>
          <div>
            <span class="info-label" style="vertical-align: top;">审核原因：</span>
            <textarea name="auditReason" placeholder="请输入审核原因" class="layui-textarea"
              style="width: 400px; height: 100px; resize: vertical;"></textarea>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 重新拆账弹窗 -->
  <div id="resplitDialog" style="display: none;">
    <div style="padding: 20px;">
      <!-- 基本信息区域 -->
      <div class="dialog-section">
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">NC客商名称：</span>
            <span class="info-value">中国邮政集团有限公司福州市分公司</span>
          </div>
          <div class="info-item">
            <span class="info-label">NC编号：</span>
            <span class="info-value">N001616</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">业务月份：</span>
            <span class="info-value">202503</span>
          </div>
          <div class="info-item">
            <span class="info-label">产品类型：</span>
            <span class="info-value">普货件</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">对应业务大类：</span>
            <span class="info-value">手续费补贴</span>
          </div>
          <div class="info-item">
            <span class="info-label">业务类型：</span>
            <span class="info-value">手续费补贴</span>
          </div>
        </div>
      </div>

      <!-- 金额信息区域 -->
      <div class="dialog-section">
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">业务金额：</span>
            <span class="info-value">10000.00</span>
          </div>
          <div class="info-item">
            <span class="info-label">调整金额：</span>
            <span class="info-value">-1000.00</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">扣印花金额：</span>
            <span class="info-value">1000.00</span>
          </div>
          <div class="info-item">
            <span class="info-label">已开票金额：</span>
            <span class="info-value">1000.00</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">已回款金额：</span>
            <span class="info-value">1000.00</span>
          </div>
          <div class="info-item">
            <span class="info-label">可拆账金额：</span>
            <span class="info-value red-text">7000.00</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item" style="flex: 2;">
            <span class="info-label">回退原因：</span>
            <span class="info-value red-text">不允许拆账给异地邮政</span>
          </div>
        </div>

        <!-- 拆账明细区域 -->
        <div class="dialog-section">
          <div style="margin-bottom: 10px; font-weight: bold;">拆账明细</div>
          <div class="info-row">
            <div class="info-item">
              <span class="info-label">拆账金额：</span>
              <span class="info-value">7000.00；</span>
            </div>
            <div class="info-item">
              <span class="info-label">拆账金额：</span>
              <span class="info-value">3000.00</span>
            </div>
          </div>

          <table class="layui-table" style="margin-top: 15px;">
            <thead>
              <tr>
                <th style="width: 80px;">序号</th>
                <th>NC客商名称</th>
                <th style="width: 120px;">NC编号</th>
                <th style="width: 120px;">拆账金额</th>
                <th style="width: 80px;">操作</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>1</td>
                <td>
                  <select class="layui-select" name="ncName1" lay-filter="ncName">
                    <option value="">请选择</option>
                    <option value="中国邮政集团有限公司三明分公司" selected>中国邮政集团有限公司三明分公司</option>
                  </select>
                </td>
                <td>
                  <input type="text" class="layui-input" name="ncCode1" value="N1224">
                </td>
                <td>
                  <input type="text" class="layui-input" name="amount1" value="2000">
                </td>
                <td>
                  <button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete-row">删除</button>
                </td>
              </tr>
              <tr>
                <td>2</td>
                <td>
                  <select class="layui-select" name="ncName2" lay-filter="ncName">
                    <option value="">请选择</option>
                    <option value="中国邮政集团有限公司三明分公司" selected>中国邮政集团有限公司三明分公司</option>
                  </select>
                </td>
                <td>
                  <input type="text" class="layui-input" name="ncCode2" value="N1225">
                </td>
                <td>
                  <input type="text" class="layui-input" name="amount2" value="5000">
                </td>
                <td>
                  <button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete-row">删除</button>
                </td>
              </tr>
            </tbody>
          </table>

          <div class="add-row" style="margin-top: 10px;">
            <a href="javascript:;" style="color: #1E9FFF;">+新增</a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script>
    layui.use(['layer', 'form'], function () {
      var layer = layui.layer;
      var form = layui.form;

      // 全局数据存储
      var globalData = {
        detailInfo: null,
        ncCustomerList: []
      };

      // 获取详情数据
      function getDetailInfo (params) {
        return new Promise(function (resolve, reject) {
          // 模拟数据（实际项目中替换为真实接口）
          var mockData = {
            ncCustomerName: '中国邮政集团有限公司福州市分公司',
            ncCode: 'N001616',
            businessMonth: '202503',
            productType: '普货件',
            businessCategory: '手续费补贴',
            businessType: '手续费补贴',
            businessAmount: '10000.00',
            adjustAmount: '-1000.00',
            stampAmount: '1000.00',
            invoicedAmount: '1000.00',
            receivedAmount: '1000.00',
            splitableAmount: '7000.00'
          };

          // 模拟异步请求
          setTimeout(function () {
            globalData.detailInfo = mockData;
            resolve(mockData);
          }, 100);
        });
      }

      // 获取NC客商列表
      function getNcCustomerList (ncName) {
        return new Promise(function (resolve, reject) {
          $.ajax({
            url: '/web_posm/feeBusinessShipment/getNcCustomerList',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
              ncName: ncName || ''
            }),
            success: function (res) {
              if (res.code === 200) {
                globalData.ncCustomerList = res.data || [];
                resolve(res.data || []);
              } else {
                layer.msg('获取NC客户数据失败: ' + res.msg, { icon: 2 });
                reject(res);
              }
            },
            error: function (xhr, status, error) {
              layer.msg('获取NC客户数据失败', { icon: 2 });
              reject(error);
            }
          });
        });
      }

      // 拆账申请弹窗
      function openApplyDialog (rowData) {
        // 显示加载提示
        var loadingIndex = layer.load(1, { shade: [0.1, '#fff'] });

        // 并行获取详情数据和NC客商列表
        Promise.all([
          getDetailInfo(rowData || {}),
          getNcCustomerList()
        ]).then(function (results) {
          var detailData = results[0];
          var customerList = results[1];

          // 关闭加载提示
          layer.close(loadingIndex);

          // 打开弹窗
          openApplyDialogWithData(detailData, customerList);
        }).catch(function (error) {
          // 关闭加载提示
          layer.close(loadingIndex);

          // 即使请求失败也要打开弹窗，使用默认数据
          var defaultDetailData = {
            ncCustomerName: '',
            ncCode: '',
            businessMonth: '',
            productType: '',
            businessCategory: '',
            businessType: '',
            businessAmount: '',
            adjustAmount: '',
            stampAmount: '',
            invoicedAmount: '',
            receivedAmount: '',
            splitableAmount: ''
          };

          // 显示错误提示但仍然打开弹窗
          layer.msg('获取数据失败，请手动填写', { icon: 2 });
          openApplyDialogWithData(defaultDetailData, []);
        });
      }

      // 打开拆账申请弹窗并填充数据
      function openApplyDialogWithData (detailData, customerList) {
        layer.open({
          type: 1,
          title: '拆账申请',
          area: ['800px', '800px'],
          content: $('#applyDialog').html(),
          btn: ['取消', '确认'],
          success: function (layero, index) {
            // 填充基本信息和金额信息
            fillApplyDialogData(layero, detailData);
            // 填充NC客商下拉选项
            fillNcCustomerOptions(layero, customerList);
            // 绑定事件
            bindApplyDialogEvents(layero);
            // 渲染表单
            form.render();
          }
        });
      }

      function openAuditDialog () {
        layer.open({
          type: 1,
          title: '拆账审核',
          area: ['800px', '800px'],
          content: $('#auditDialog').html(),
          btn: ['取消', '确认'],
          success: function (layero, index) {
            form.render();
          }
        });
      }

      function openResplitDialog () {
        layer.open({
          type: 1,
          title: '重新拆账',
          area: ['800px', '800px'],
          content: $('#resplitDialog').html(),
          btn: ['取消', '确认'],
          success: function (layero, index) {
            // 绑定新增和删除事件
            bindDialogEvents(layero);
            form.render();
          }
        });
      }

      // 填充拆账申请弹窗数据
      function fillApplyDialogData (layero, data) {
        layero.find('.info-value').each(function () {
          var field = $(this).text();
          switch (field) {
            case '中国邮政集团有限公司福州市分公司':
              $(this).text(data.ncCustomerName);
              break;
            case 'N001616':
              $(this).text(data.ncCode);
              break;
            case '202503':
              $(this).text(data.businessMonth);
              break;
            case '普货件':
              $(this).text(data.productType);
              break;
            case '手续费补贴':
              if ($(this).prev('.info-label').text().indexOf('对应业务大类') > -1) {
                $(this).text(data.businessCategory);
              } else if ($(this).prev('.info-label').text().indexOf('业务类型') > -1) {
                $(this).text(data.businessType);
              }
              break;
            case '10000.00':
              $(this).text(data.businessAmount);
              break;
            case '-1000.00':
              $(this).text(data.adjustAmount);
              break;
            case '1000.00':
              if ($(this).prev('.info-label').text().indexOf('扣印花') > -1) {
                $(this).text(data.stampAmount);
              } else if ($(this).prev('.info-label').text().indexOf('已开票') > -1) {
                $(this).text(data.invoicedAmount);
              } else if ($(this).prev('.info-label').text().indexOf('已回款') > -1) {
                $(this).text(data.receivedAmount);
              }
              break;
            case '7000.00':
              $(this).text(data.splitableAmount);
              break;
          }
        });
      }

      // 填充NC客商下拉选项
      function fillNcCustomerOptions (layero, customerList) {
        var selects = layero.find('select[name^="ncName"]');
        selects.each(function () {
          var select = $(this);
          select.empty();
          select.append('<option value="">请选择或输入搜索NC客商</option>');

          if (customerList && customerList.length > 0) {
            for (var i = 0; i < customerList.length; i++) {
              var customer = customerList[i];
              select.append('<option value="' + customer.ncCode + '" data-name="' + customer.ncCustomerName + '">' + customer.ncCustomerName + '</option>');
            }
          }
        });

        // 存储客商列表供搜索使用
        globalData.ncCustomerList = customerList || [];
      }



      // 绑定拆账申请弹窗事件
      function bindApplyDialogEvents (layero) {
        // 新增按钮事件
        layero.find('.add-row a').off('click').on('click', function (e) {
          e.preventDefault();
          addNewRow(layero);
        });

        // 删除按钮事件
        layero.off('click', '.delete-row').on('click', '.delete-row', function (e) {
          e.preventDefault();
          deleteTableRow(this, layero);
        });

        // NC客商选择事件
        form.on('select(ncName)', function (data) {
          var selectedOption = $(data.elem).find('option:selected');
          var ncCode = selectedOption.val();
          var ncName = selectedOption.data('name');
          var rowNum = $(data.elem).data('row');

          if (ncCode) {
            // 自动填入NC编号
            layero.find('input[name="ncCode' + rowNum + '"]').val(ncCode);
          } else {
            // 清空NC编号
            layero.find('input[name="ncCode' + rowNum + '"]').val('');
          }
        });
      }

      // 绑定弹窗内的事件（其他弹窗使用）
      function bindDialogEvents (layero) {
        // 新增按钮事件
        layero.find('.add-row a').off('click').on('click', function (e) {
          e.preventDefault();
          addNewRow(layero);
        });

        // 删除按钮事件
        layero.off('click', '.delete-row').on('click', '.delete-row', function (e) {
          e.preventDefault();
          deleteTableRow(this, layero);
        });
      }

      // 添加新行
      function addNewRow (layero) {
        var tbody = layero.find('tbody');
        var rowCount = tbody.find('tr').length + 1;

        // 构建NC客商选项
        var customerOptions = '<option value="">请选择或输入搜索NC客商</option>';
        if (globalData.ncCustomerList && globalData.ncCustomerList.length > 0) {
          for (var i = 0; i < globalData.ncCustomerList.length; i++) {
            var customer = globalData.ncCustomerList[i];
            customerOptions += '<option value="' + customer.ncCode + '" data-name="' + customer.ncCustomerName + '">' + customer.ncCustomerName + '</option>';
          }
        }

        var newRow = '<tr>' +
          '<td>' + rowCount + '</td>' +
          '<td>' +
          '<select class="layui-select" name="ncName' + rowCount + '" lay-filter="ncName" lay-search data-row="' + rowCount + '">' +
          customerOptions +
          '</select>' +
          '</td>' +
          '<td>' +
          '<input type="text" class="layui-input" name="ncCode' + rowCount + '" readonly placeholder="NC编号">' +
          '</td>' +
          '<td>' +
          '<input type="text" class="layui-input" name="amount' + rowCount + '" placeholder="请输入拆账金额">' +
          '</td>' +
          '<td>' +
          '<button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete-row">删除</button>' +
          '</td>' +
          '</tr>';
        tbody.append(newRow);
        form.render('select');
      }

      // 删除行
      function deleteTableRow (btn, layero) {
        var tbody = layero.find('tbody');
        var rows = tbody.find('tr');

        if (rows.length <= 1) {
          layer.msg('至少需要保留一条拆账明细记录', { icon: 2 });
          return;
        }

        $(btn).closest('tr').remove();

        // 重新编号
        tbody.find('tr').each(function (index) {
          $(this).find('td:first').text(index + 1);
        });
      }

      // 绑定主页面按钮事件
      document.getElementById('splitApply').addEventListener('click', openApplyDialog);
      document.getElementById('splitAudit').addEventListener('click', openAuditDialog);
      document.getElementById('reSplit').addEventListener('click', openResplitDialog);
    });
  </script>
</body>

</html>