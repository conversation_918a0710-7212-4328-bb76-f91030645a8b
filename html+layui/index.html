<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <title>费用业务运费拆账</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link rel="stylesheet" href="https://unpkg.com/layui@2.8.18/dist/css/layui.css">
  <style>
    .container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .section {
      margin-bottom: 30px;
      border: 1px solid #e6e6e6;
      border-radius: 5px;
      padding: 20px;
    }

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }

    .info-row {
      display: flex;
      margin-bottom: 10px;
    }

    .info-item {
      flex: 1;
      margin-right: 20px;
    }

    .info-item:last-child {
      margin-right: 0;
    }

    .info-label {
      display: inline-block;
      width: 100px;
      color: #666;
    }

    .info-value {
      color: #333;
    }

    .red-text {
      color: #ff0000;
    }

    .action-buttons {
      text-align: center;
      margin-top: 20px;
    }

    .action-buttons .layui-btn {
      margin: 0 10px;
    }

    .split-table {
      margin-top: 15px;
    }

    .add-row {
      color: #1E9FFF;
      cursor: pointer;
      margin-top: 10px;
    }

    .add-row:hover {
      text-decoration: underline;
    }

    .dialog-section {
      margin-bottom: 20px;
    }

    .readonly-text {
      display: inline-block;
    }

    .editable-input,
    .editable-select {
      width: 100%;
    }

    /* 弹窗内容样式 */
    #splitDialog {
      padding: 20px !important;
    }

    /* 确保弹窗内的表格有适当间距 */
    #splitDialog .layui-table {
      margin: 15px 0;
    }

    /* 弹窗内信息行的间距 */
    #splitDialog .info-row {
      margin-bottom: 12px;
    }

    /* 审核区域的样式 */
    #auditSection {
      border-top: 1px solid #e6e6e6;
      padding-top: 15px;
    }
  </style>
</head>

<body>
  <div class="container">
    <!-- 基本信息 -->
    <div class="section">
      <div class="section-title">基本信息</div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">NC客商名称：</span>
          <span class="info-value">中国邮政集团有限公司福州市分公司</span>
        </div>
        <div class="info-item">
          <span class="info-label">NC编号：</span>
          <span class="info-value">N001616</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">业务月份：</span>
          <span class="info-value">202503</span>
        </div>
        <div class="info-item">
          <span class="info-label">产品类型：</span>
          <span class="info-value">普货件</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">对应业务大类：</span>
          <span class="info-value">手续费补贴</span>
        </div>
        <div class="info-item">
          <span class="info-label">业务类型：</span>
          <span class="info-value">手续费补贴</span>
        </div>
      </div>
    </div>

    <!-- 金额信息 -->
    <div class="section">
      <div class="section-title">金额信息</div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">业务金额：</span>
          <span class="info-value">10000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">调整金额：</span>
          <span class="info-value">-1000.00</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">扣印花金额：</span>
          <span class="info-value">1000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">已开票金额：</span>
          <span class="info-value">1000.00</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">已回款金额：</span>
          <span class="info-value">1000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">可拆账金额：</span>
          <span class="info-value red-text">7000.00</span>
        </div>
      </div>

    </div>
  </div>

  <!-- 操作按钮 -->
  <div class="action-buttons">
    <button type="button" class="layui-btn layui-btn-normal" id="splitApply">拆账申请</button>
    <button type="button" class="layui-btn layui-btn-warm" id="splitAudit">拆账审核</button>
    <button type="button" class="layui-btn" id="reSplit">重新拆账</button>
  </div>
  </div>

  <!-- 弹窗模板 -->
  <div id="splitDialog" style="display: none;">
    <!-- 基本信息区域 -->
    <div class="dialog-section">
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">NC客商名称：</span>
          <span class="info-value" data-field="ncCustomerName">中国邮政集团有限公司福州市分公司</span>
        </div>
        <div class="info-item">
          <span class="info-label">NC编号：</span>
          <span class="info-value" data-field="ncCode">N001616</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">业务月份：</span>
          <span class="info-value" data-field="businessMonth">202503</span>
        </div>
        <div class="info-item">
          <span class="info-label">产品类型：</span>
          <span class="info-value" data-field="productType">普货件</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">对应业务大类：</span>
          <span class="info-value" data-field="businessCategory">手续费补贴</span>
        </div>
        <div class="info-item">
          <span class="info-label">业务类型：</span>
          <span class="info-value" data-field="businessType">手续费补贴</span>
        </div>
      </div>
    </div>

    <!-- 金额信息区域 -->
    <div class="dialog-section">
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">业务金额：</span>
          <span class="info-value" data-field="businessAmount">10000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">调整金额：</span>
          <span class="info-value" data-field="adjustAmount">-1000.00</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">扣印花金额：</span>
          <span class="info-value" data-field="stampAmount">1000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">已开票金额：</span>
          <span class="info-value" data-field="invoicedAmount">1000.00</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">已回款金额：</span>
          <span class="info-value" data-field="receivedAmount">1000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">可拆账金额：</span>
          <span class="info-value red-text" data-field="splitableAmount">7000.00</span>
        </div>
      </div>
      <div class="info-row" id="rejectReasonRow" style="display: none;">
        <div class="info-item" style="flex: 2;">
          <span class="info-label">回退原因：</span>
          <span class="info-value red-text" data-field="rejectReason">不允许拆账给异地邮政</span>
        </div>
      </div>

      <!-- 拆账明细区域 -->
      <div class="dialog-section">
        <div style="margin-bottom: 10px; font-weight: bold;">拆账明细</div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">拆账金额：</span>
            <span class="info-value">7000.00；</span>
          </div>
          <div class="info-item">
            <span class="info-label">拆账金额：</span>
            <span class="info-value">3000.00</span>
          </div>
        </div>

        <table class="layui-table" style="margin-top: 15px;">
          <thead>
            <tr>
              <th style="width: 80px;">序号</th>
              <th>NC客商名称</th>
              <th style="width: 120px;">NC编号</th>
              <th style="width: 120px;">拆账金额</th>
              <th id="actionColumn" style="width: 80px; display: none;">操作</th>
            </tr>
          </thead>
          <tbody id="splitTableBody">
            <tr>
              <td>1</td>
              <td>
                <span class="readonly-text">中国邮政集团有限公司三明分公司</span>
                <select class="editable-select layui-hide" name="ncName1" lay-filter="ncName">
                  <option value="">请选择</option>
                  <option value="中国邮政集团有限公司三明分公司" selected>中国邮政集团有限公司三明分公司</option>
                </select>
              </td>
              <td>
                <span class="readonly-text">N1224</span>
                <input type="text" class="editable-input layui-input layui-hide" name="ncCode1" value="N1224">
              </td>
              <td>
                <span class="readonly-text">2000</span>
                <input type="text" class="editable-input layui-input layui-hide" name="amount1" value="2000">
              </td>
              <td class="action-cell" style="display: none;">
                <button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete-row">删除</button>
              </td>
            </tr>
            <tr>
              <td>2</td>
              <td>
                <span class="readonly-text">中国邮政集团有限公司三明分公司</span>
                <select class="editable-select layui-hide" name="ncName2" lay-filter="ncName">
                  <option value="">请选择</option>
                  <option value="中国邮政集团有限公司三明分公司" selected>中国邮政集团有限公司三明分公司</option>
                </select>
              </td>
              <td>
                <span class="readonly-text">N1225</span>
                <input type="text" class="editable-input layui-input layui-hide" name="ncCode2" value="N1225">
              </td>
              <td>
                <span class="readonly-text">5000</span>
                <input type="text" class="editable-input layui-input layui-hide" name="amount2" value="5000">
              </td>
              <td class="action-cell" style="display: none;">
                <button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete-row">删除</button>
              </td>
            </tr>
          </tbody>
        </table>

        <div id="addRowBtn" class="add-row" style="margin-top: 10px; display: none;">
          <a href="javascript:;" style="color: #1E9FFF;">+新增</a>
        </div>
      </div>

      <!-- 审核区域 -->
      <div id="auditSection" class="dialog-section" style="display: none;">
        <div style="margin-bottom: 15px;">
          <span class="info-label">审核结果：</span>
          <select name="auditResult" lay-filter="auditResult" style="width: 200px;">
            <option value="">请选择审核结果</option>
            <option value="pass">审核通过</option>
            <option value="reject">审核拒绝</option>
          </select>
        </div>
        <div>
          <span class="info-label" style="vertical-align: top;">审核原因：</span>
          <textarea name="auditReason" placeholder="请输入审核原因" class="layui-textarea"
            style="width: 400px; height: 100px; resize: vertical;"></textarea>
        </div>
      </div>
    </div>

    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
      layui.use(['layer', 'form'], function () {
        var layer = layui.layer;
        var form = layui.form;

        // 全局数据存储
        var globalData = {
          detailInfo: null,           // 详情数据
          ncCustomerList: [],         // NC客商列表
          currentDialogType: null,    // 当前弹窗类型
          isDialogOpening: false      // 防止重复打开弹窗
        };

        // 清理全局状态
        function cleanupGlobalState () {
          globalData.currentDialogType = null;
          globalData.isDialogOpening = false;
          // 注意：不清理detailInfo和ncCustomerList，因为这些数据可以复用
        }

        // API接口配置
        var apiConfig = {
          // 获取详情数据接口
          getDetailInfo: {
            url: '/api/splitAccount/getDetailInfo',  // 待确定
            method: 'POST'
          },
          // 获取NC客商列表接口
          getNcCustomerList: {
            url: '/api/splitAccount/getNcCustomerList',  // 待确定
            method: 'POST'
          }
        };

        // 获取详情数据
        function getDetailInfo (params) {
          return new Promise(function (resolve, reject) {
            // 模拟数据（当接口不可用时使用）
            var mockData = {
              ncCustomerName: '中国邮政集团有限公司福州市分公司',
              ncCode: 'N001616',
              businessMonth: '202503',
              productType: '普货件',
              businessCategory: '手续费补贴',
              businessType: '手续费补贴',
              businessAmount: '10000.00',
              adjustAmount: '-1000.00',
              stampAmount: '1000.00',
              invoicedAmount: '1000.00',
              receivedAmount: '1000.00',
              splitableAmount: '7000.00',
              rejectReason: '不允许拆账给异地邮政',
              splitDetails: [
                {
                  ncCustomerName: '中国邮政集团有限公司三明分公司',
                  ncCode: 'N1224',
                  splitAmount: '2000'
                },
                {
                  ncCustomerName: '中国邮政集团有限公司三明分公司',
                  ncCode: 'N1225',
                  splitAmount: '5000'
                }
              ]
            };

            $.ajax({
              url: apiConfig.getDetailInfo.url,
              type: apiConfig.getDetailInfo.method,
              contentType: 'application/json',
              data: JSON.stringify(params || {}),
              success: function (res) {
                if (res.code === 200) {
                  globalData.detailInfo = res.data;
                  resolve(res.data);
                } else {
                  layer.msg('获取详情数据失败: ' + res.msg, { icon: 2 });
                  reject(res);
                }
              },
              error: function (xhr, status, error) {
                // 接口调用失败时使用模拟数据
                console.warn('详情接口调用失败，使用模拟数据');
                globalData.detailInfo = mockData;
                resolve(mockData);
              }
            });
          });
        }

        // 获取NC客商列表
        function getNcCustomerList (params) {
          return new Promise(function (resolve, reject) {
            // 模拟数据（当接口不可用时使用）
            var mockData = [
              {
                ncCode: 'N1224',
                ncCustomerName: '中国邮政集团有限公司三明分公司'
              },
              {
                ncCode: 'N1225',
                ncCustomerName: '中国邮政集团有限公司厦门分公司'
              },
              {
                ncCode: 'N1226',
                ncCustomerName: '中国邮政集团有限公司泉州分公司'
              },
              {
                ncCode: 'N1227',
                ncCustomerName: '中国邮政集团有限公司漳州分公司'
              }
            ];

            $.ajax({
              url: apiConfig.getNcCustomerList.url,
              type: apiConfig.getNcCustomerList.method,
              contentType: 'application/json',
              data: JSON.stringify(params || {}),
              success: function (res) {
                if (res.code === 200) {
                  globalData.ncCustomerList = res.data || [];
                  resolve(res.data);
                } else {
                  layer.msg('获取NC客户数据失败: ' + res.msg, { icon: 2 });
                  reject(res);
                }
              },
              error: function (xhr, status, error) {
                // 接口调用失败时使用模拟数据
                console.warn('NC客商列表接口调用失败，使用模拟数据');
                globalData.ncCustomerList = mockData;
                resolve(mockData);
              }
            });
          });
        }

        // 弹窗配置
        var dialogConfig = {
          apply: {
            title: '拆账申请',
            readonly: false,
            showAudit: false,
            showReject: false,
            showActions: true
          },
          audit: {
            title: '拆账审核',
            readonly: true,
            showAudit: true,
            showReject: false,
            showActions: false
          },
          resplit: {
            title: '重新拆账',
            readonly: false,
            showAudit: false,
            showReject: true,
            showActions: true
          }
        };

        // 弹窗实例管理
        var dialogInstances = {
          apply: null,
          audit: null,
          resplit: null
        };

        // 打开弹窗函数 - 彻底重置弹窗状态
        function openSplitDialog (type) {
          var config = dialogConfig[type];

          // 如果弹窗已经存在，先关闭
          if (dialogInstances[type]) {
            layer.close(dialogInstances[type]);
            dialogInstances[type] = null;
          }

          // 准备模拟数据
          var mockData = {
            ncCustomerName: '中国邮政集团有限公司福州市分公司',
            ncCode: 'N001616',
            businessMonth: '202503',
            productType: '普货件',
            businessCategory: '手续费补贴',
            businessType: '手续费补贴',
            businessAmount: '10000.00',
            adjustAmount: '-1000.00',
            stampAmount: '1000.00',
            invoicedAmount: '1000.00',
            receivedAmount: '1000.00',
            splitableAmount: '7000.00',
            rejectReason: '不允许拆账给异地邮政'
          };

          // 获取原始弹窗的HTML内容并重置所有表单状态
          var originalDialog = document.getElementById('splitDialog');

          // 先重置原始弹窗中的所有表单元素
          resetDialogForm(originalDialog);

          var dialogHTML = originalDialog.innerHTML;

          // 创建包装div并设置样式
          var wrappedContent = '<div style="padding: 20px;">' + dialogHTML + '</div>';

          // 打开弹窗
          var layerIndex = layer.open({
            type: 1,
            title: config.title,
            area: ['800px', '800px'],
            content: wrappedContent,
            btn: ['取消', '确认'],
            success: function (layero, index) {
              // 存储弹窗实例
              dialogInstances[type] = index;

              // 再次重置弹窗中的表单元素（确保彻底清理）
              resetDialogForm(layero[0]);

              // 填充数据
              fillDialogData(layero, mockData);
              // 设置模式
              setMode(layero, config);
              // 绑定事件
              bindEvents(layero);
              // 渲染表单
              form.render();
            },
            end: function () {
              // 弹窗关闭时清理实例引用
              dialogInstances[type] = null;
            }
          });
        }

        // 重置弹窗表单状态的函数
        function resetDialogForm (container) {
          // 重置所有input元素
          var inputs = container.querySelectorAll('input[type="text"]');
          for (var i = 0; i < inputs.length; i++) {
            inputs[i].value = '';
          }

          // 重置所有select元素
          var selects = container.querySelectorAll('select');
          for (var i = 0; i < selects.length; i++) {
            selects[i].selectedIndex = 0;
          }

          // 重置所有textarea元素
          var textareas = container.querySelectorAll('textarea');
          for (var i = 0; i < textareas.length; i++) {
            textareas[i].value = '';
          }

          // 重置表格内容到初始状态 - 不使用模板字符串
          var tbody = container.querySelector('#splitTableBody');
          if (tbody) {
            var initialTableHTML =
              '<tr>' +
              '<td>1</td>' +
              '<td>' +
              '<span class="readonly-text">中国邮政集团有限公司三明分公司</span>' +
              '<select class="editable-select layui-hide" name="ncName1" lay-filter="ncName">' +
              '<option value="">请选择</option>' +
              '<option value="中国邮政集团有限公司三明分公司" selected>中国邮政集团有限公司三明分公司</option>' +
              '</select>' +
              '</td>' +
              '<td>' +
              '<span class="readonly-text">N1224</span>' +
              '<input type="text" class="editable-input layui-input layui-hide" name="ncCode1" value="N1224">' +
              '</td>' +
              '<td>' +
              '<span class="readonly-text">2000</span>' +
              '<input type="text" class="editable-input layui-input layui-hide" name="amount1" value="2000">' +
              '</td>' +
              '<td class="action-cell" style="display: none;">' +
              '<button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete-row">删除</button>' +
              '</td>' +
              '</tr>' +
              '<tr>' +
              '<td>2</td>' +
              '<td>' +
              '<span class="readonly-text">中国邮政集团有限公司三明分公司</span>' +
              '<select class="editable-select layui-hide" name="ncName2" lay-filter="ncName">' +
              '<option value="">请选择</option>' +
              '<option value="中国邮政集团有限公司三明分公司" selected>中国邮政集团有限公司三明分公司</option>' +
              '</select>' +
              '</td>' +
              '<td>' +
              '<span class="readonly-text">N1225</span>' +
              '<input type="text" class="editable-input layui-input layui-hide" name="ncCode2" value="N1225">' +
              '</td>' +
              '<td>' +
              '<span class="readonly-text">5000</span>' +
              '<input type="text" class="editable-input layui-input layui-hide" name="amount2" value="5000">' +
              '</td>' +
              '<td class="action-cell" style="display: none;">' +
              '<button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete-row">删除</button>' +
              '</td>' +
              '</tr>';
            tbody.innerHTML = initialTableHTML;
          }
        }

        // 最简单的数据填充
        function fillDialogData (layero, data) {
          layero.find('[data-field="ncCustomerName"]').text(data.ncCustomerName);
          layero.find('[data-field="ncCode"]').text(data.ncCode);
          layero.find('[data-field="businessMonth"]').text(data.businessMonth);
          layero.find('[data-field="productType"]').text(data.productType);
          layero.find('[data-field="businessCategory"]').text(data.businessCategory);
          layero.find('[data-field="businessType"]').text(data.businessType);
          layero.find('[data-field="businessAmount"]').text(data.businessAmount);
          layero.find('[data-field="adjustAmount"]').text(data.adjustAmount);
          layero.find('[data-field="stampAmount"]').text(data.stampAmount);
          layero.find('[data-field="invoicedAmount"]').text(data.invoicedAmount);
          layero.find('[data-field="receivedAmount"]').text(data.receivedAmount);
          layero.find('[data-field="splitableAmount"]').text(data.splitableAmount);
          layero.find('[data-field="rejectReason"]').text(data.rejectReason);
        }

        // 最简单的模式设置
        function setMode (layero, config) {
          if (config.showAudit) {
            layero.find('#auditSection').show();
          } else {
            layero.find('#auditSection').hide();
          }

          if (config.showReject) {
            layero.find('#rejectReasonRow').show();
          } else {
            layero.find('#rejectReasonRow').hide();
          }

          if (config.showActions) {
            layero.find('#addRowBtn').show();
            layero.find('#actionColumn').show();
            layero.find('.action-cell').show();
          } else {
            layero.find('#addRowBtn').hide();
            layero.find('#actionColumn').hide();
            layero.find('.action-cell').hide();
          }

          if (config.readonly) {
            layero.find('.readonly-text').show();
            layero.find('.editable-input').hide();
            layero.find('.editable-select').hide();
          } else {
            layero.find('.readonly-text').hide();
            layero.find('.editable-input').show();
            layero.find('.editable-select').show();
          }
        }

        // 事件绑定 - 确保不重复绑定
        function bindEvents (layero) {
          // 先移除所有可能存在的事件监听器
          layero.find('#addRowBtn a').off('click.splitDialog');
          layero.off('click.splitDialog', '.delete-row');

          // 新增按钮事件
          layero.find('#addRowBtn a').on('click.splitDialog', function (e) {
            e.preventDefault();
            addNewRow(layero);
          });

          // 删除按钮事件 - 使用事件委托和命名空间
          layero.on('click.splitDialog', '.delete-row', function (e) {
            e.preventDefault();
            deleteTableRow(this, layero);
          });
        }

        // 最简单的添加行
        function addNewRow (layero) {
          var tbody = layero.find('#splitTableBody');
          var rowCount = tbody.find('tr').length + 1;
          var newRow = '<tr>' +
            '<td>' + rowCount + '</td>' +
            '<td><span class="readonly-text" style="display: none;"></span><select class="editable-select" name="ncName' + rowCount + '"><option value="">请选择</option><option value="中国邮政集团有限公司三明分公司">中国邮政集团有限公司三明分公司</option></select></td>' +
            '<td><span class="readonly-text" style="display: none;"></span><input type="text" class="editable-input layui-input" name="ncCode' + rowCount + '"></td>' +
            '<td><span class="readonly-text" style="display: none;"></span><input type="text" class="editable-input layui-input" name="amount' + rowCount + '"></td>' +
            '<td class="action-cell"><button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete-row">删除</button></td>' +
            '</tr>';
          tbody.append(newRow);
          form.render('select');
        }

        // 最简单的删除行
        function deleteTableRow (btn, layero) {
          var tbody = layero.find('#splitTableBody');
          var rows = tbody.find('tr');

          if (rows.length <= 1) {
            layer.msg('至少需要保留一条拆账明细记录', { icon: 2 });
            return;
          }

          $(btn).closest('tr').remove();

          // 重新编号
          tbody.find('tr').each(function (index) {
            $(this).find('td:first').text(index + 1);
          });
        }











        // 绑定主页面按钮事件
        document.getElementById('splitApply').addEventListener('click', function () {
          openSplitDialog('apply');
        });

        document.getElementById('splitAudit').addEventListener('click', function () {
          openSplitDialog('audit');
        });

        document.getElementById('reSplit').addEventListener('click', function () {
          openSplitDialog('resplit');
        });
      });
    </script>
</body>

</html>