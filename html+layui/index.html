<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <title>费用业务运费拆账</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link rel="stylesheet" href="https://unpkg.com/layui@2.8.18/dist/css/layui.css">
  <style>
    .container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .section {
      margin-bottom: 30px;
      border: 1px solid #e6e6e6;
      border-radius: 5px;
      padding: 20px;
    }

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }

    .info-row {
      display: flex;
      margin-bottom: 10px;
    }

    .info-item {
      flex: 1;
      margin-right: 20px;
    }

    .info-item:last-child {
      margin-right: 0;
    }

    .info-label {
      display: inline-block;
      width: 100px;
      color: #666;
    }

    .info-value {
      color: #333;
    }

    .red-text {
      color: #ff0000;
    }

    .action-buttons {
      text-align: center;
      margin-top: 20px;
    }

    .action-buttons .layui-btn {
      margin: 0 10px;
    }

    .split-table {
      margin-top: 15px;
    }

    .add-row {
      color: #1E9FFF;
      cursor: pointer;
      margin-top: 10px;
    }

    .add-row:hover {
      text-decoration: underline;
    }

    .dialog-section {
      margin-bottom: 20px;
    }

    .readonly-text {
      display: inline-block;
    }

    .editable-input,
    .editable-select {
      width: 100%;
    }

    /* 弹窗内容样式 */
    #splitDialog {
      padding: 20px !important;
    }

    /* 确保弹窗内的表格有适当间距 */
    #splitDialog .layui-table {
      margin: 15px 0;
    }

    /* 弹窗内信息行的间距 */
    #splitDialog .info-row {
      margin-bottom: 12px;
    }

    /* 审核区域的样式 */
    #auditSection {
      border-top: 1px solid #e6e6e6;
      padding-top: 15px;
    }

    /* NC客商搜索容器样式 */
    .nc-customer-container {
      position: relative;
    }

    .nc-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border: 1px solid #e6e6e6;
      border-top: none;
      max-height: 200px;
      overflow-y: auto;
      z-index: 9999;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
    }

    .nc-dropdown-item {
      padding: 8px 12px;
      cursor: pointer;
      border-bottom: 1px solid #f5f5f5;
    }

    .nc-dropdown-item:hover {
      background-color: #f2f2f2;
    }

    .nc-dropdown-item:last-child {
      border-bottom: none;
    }

    .nc-dropdown-item.loading {
      color: #999;
      text-align: center;
    }

    .nc-dropdown-item.no-data {
      color: #999;
      text-align: center;
    }
  </style>
</head>

<body>
  <div class="container">
    <!-- 基本信息 -->
    <div class="section">
      <div class="section-title">基本信息</div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">NC客商名称：</span>
          <span class="info-value">中国邮政集团有限公司福州市分公司</span>
        </div>
        <div class="info-item">
          <span class="info-label">NC编号：</span>
          <span class="info-value">N001616</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">业务月份：</span>
          <span class="info-value">202503</span>
        </div>
        <div class="info-item">
          <span class="info-label">产品类型：</span>
          <span class="info-value">普货件</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">对应业务大类：</span>
          <span class="info-value">手续费补贴</span>
        </div>
        <div class="info-item">
          <span class="info-label">业务类型：</span>
          <span class="info-value">手续费补贴</span>
        </div>
      </div>
    </div>

    <!-- 金额信息 -->
    <div class="section">
      <div class="section-title">金额信息</div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">业务金额：</span>
          <span class="info-value">10000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">调整金额：</span>
          <span class="info-value">-1000.00</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">扣印花金额：</span>
          <span class="info-value">1000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">已开票金额：</span>
          <span class="info-value">1000.00</span>
        </div>
      </div>
      <div class="info-row">
        <div class="info-item">
          <span class="info-label">已回款金额：</span>
          <span class="info-value">1000.00</span>
        </div>
        <div class="info-item">
          <span class="info-label">可拆账金额：</span>
          <span class="info-value red-text">7000.00</span>
        </div>
      </div>

    </div>
  </div>

  <!-- 操作按钮 -->
  <div class="action-buttons">
    <button type="button" class="layui-btn layui-btn-normal" id="splitApply">拆账申请</button>
    <button type="button" class="layui-btn layui-btn-warm" id="splitAudit">拆账审核</button>
    <button type="button" class="layui-btn" id="reSplit">重新拆账</button>
  </div>
  </div>

  <!-- 拆账申请弹窗 -->
  <div id="applyDialog" style="display: none;">
    <div style="padding: 20px;">
      <!-- 基本信息区域 -->
      <div class="dialog-section">
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">NC客商名称：</span>
            <span class="info-value">中国邮政集团有限公司福州市分公司</span>
          </div>
          <div class="info-item">
            <span class="info-label">NC编号：</span>
            <span class="info-value">N001616</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">业务月份：</span>
            <span class="info-value">202503</span>
          </div>
          <div class="info-item">
            <span class="info-label">产品类型：</span>
            <span class="info-value">普货件</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">对应业务大类：</span>
            <span class="info-value">手续费补贴</span>
          </div>
          <div class="info-item">
            <span class="info-label">业务类型：</span>
            <span class="info-value">手续费补贴</span>
          </div>
        </div>
      </div>

      <!-- 金额信息区域 -->
      <div class="dialog-section">
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">业务金额：</span>
            <span class="info-value">10000.00</span>
          </div>
          <div class="info-item">
            <span class="info-label">调整金额：</span>
            <span class="info-value">-1000.00</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">扣印花金额：</span>
            <span class="info-value">1000.00</span>
          </div>
          <div class="info-item">
            <span class="info-label">已开票金额：</span>
            <span class="info-value">1000.00</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">已回款金额：</span>
            <span class="info-value">1000.00</span>
          </div>
          <div class="info-item">
            <span class="info-label">可拆账金额：</span>
            <span class="info-value red-text">7000.00</span>
          </div>
        </div>

        <!-- 拆账明细区域 -->
        <div class="dialog-section">
          <div style="margin-bottom: 10px; font-weight: bold;">拆账明细</div>
          <div class="info-row">
            <div class="info-item">
              <span class="info-label">拆账金额：</span>
              <span class="info-value">7000.00；</span>
            </div>
            <div class="info-item">
              <span class="info-label">拆账金额：</span>
              <span class="info-value">3000.00</span>
            </div>
          </div>

          <table class="layui-table" style="margin-top: 15px;">
            <thead>
              <tr>
                <th style="width: 80px;">序号</th>
                <th>NC客商名称</th>
                <th style="width: 120px;">NC编号</th>
                <th style="width: 120px;">拆账金额</th>
                <th style="width: 80px;">操作</th>
              </tr>
            </thead>
            <tbody id="applyTableBody">
              <tr>
                <td>1</td>
                <td>
                  <div class="nc-customer-container" data-row="1">
                    <input type="text" class="layui-input nc-customer-input" name="ncName1" placeholder="请输入NC客商名称搜索"
                      autocomplete="off" data-row="1">
                    <input type="hidden" name="ncNameValue1" value="">
                    <div class="nc-dropdown" style="display: none;"></div>
                  </div>
                </td>
                <td>
                  <input type="text" class="layui-input" name="ncCode1" readonly placeholder="NC编号">
                </td>
                <td>
                  <input type="text" class="layui-input" name="amount1" placeholder="请输入拆账金额">
                </td>
                <td>
                  <button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete-row">删除</button>
                </td>
              </tr>
            </tbody>
          </table>

          <div class="add-row" style="margin-top: 10px;">
            <a href="javascript:;" style="color: #1E9FFF;">+新增</a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 拆账审核弹窗 -->
  <div id="auditDialog" style="display: none;">
    <div style="padding: 20px;">
      <!-- 基本信息区域 -->
      <div class="dialog-section">
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">NC客商名称：</span>
            <span class="info-value">中国邮政集团有限公司福州市分公司</span>
          </div>
          <div class="info-item">
            <span class="info-label">NC编号：</span>
            <span class="info-value">N001616</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">业务月份：</span>
            <span class="info-value">202503</span>
          </div>
          <div class="info-item">
            <span class="info-label">产品类型：</span>
            <span class="info-value">普货件</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">对应业务大类：</span>
            <span class="info-value">手续费补贴</span>
          </div>
          <div class="info-item">
            <span class="info-label">业务类型：</span>
            <span class="info-value">手续费补贴</span>
          </div>
        </div>
      </div>

      <!-- 金额信息区域 -->
      <div class="dialog-section">
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">业务金额：</span>
            <span class="info-value">10000.00</span>
          </div>
          <div class="info-item">
            <span class="info-label">调整金额：</span>
            <span class="info-value">-1000.00</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">扣印花金额：</span>
            <span class="info-value">1000.00</span>
          </div>
          <div class="info-item">
            <span class="info-label">已开票金额：</span>
            <span class="info-value">1000.00</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">已回款金额：</span>
            <span class="info-value">1000.00</span>
          </div>
          <div class="info-item">
            <span class="info-label">可拆账金额：</span>
            <span class="info-value red-text">7000.00</span>
          </div>
        </div>

        <!-- 拆账明细区域 -->
        <div class="dialog-section">
          <div style="margin-bottom: 10px; font-weight: bold;">拆账明细</div>
          <div class="info-row">
            <div class="info-item">
              <span class="info-label">拆账金额：</span>
              <span class="info-value">7000.00；</span>
            </div>
            <div class="info-item">
              <span class="info-label">拆账金额：</span>
              <span class="info-value">3000.00</span>
            </div>
          </div>

          <table class="layui-table" style="margin-top: 15px;">
            <thead>
              <tr>
                <th style="width: 80px;">序号</th>
                <th>NC客商名称</th>
                <th style="width: 120px;">NC编号</th>
                <th style="width: 120px;">拆账金额</th>
              </tr>
            </thead>
            <tbody id="auditTableBody">
              <!-- 审核明细数据将通过接口动态填充 -->
            </tbody>
          </table>
        </div>

        <!-- 审核区域 -->
        <div class="dialog-section" style="border-top: 1px solid #e6e6e6; padding-top: 15px;">
          <div style="margin-bottom: 15px;">
            <span class="info-label">审核结果：</span>
            <select name="auditResult" lay-filter="auditResult" style="width: 200px;">
              <option value="">请选择审核结果</option>
              <option value="4">审批通过</option>
              <option value="3">审批回退</option>
            </select>
          </div>
          <div>
            <span class="info-label" style="vertical-align: top;">审核原因：</span>
            <textarea name="auditReason" placeholder="请输入审核原因" class="layui-textarea"
              style="width: 400px; height: 100px; resize: vertical;"></textarea>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 重新拆账弹窗 -->
  <div id="resplitDialog" style="display: none;">
    <div style="padding: 20px;">
      <!-- 基本信息区域 -->
      <div class="dialog-section">
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">NC客商名称：</span>
            <span class="info-value">中国邮政集团有限公司福州市分公司</span>
          </div>
          <div class="info-item">
            <span class="info-label">NC编号：</span>
            <span class="info-value">N001616</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">业务月份：</span>
            <span class="info-value">202503</span>
          </div>
          <div class="info-item">
            <span class="info-label">产品类型：</span>
            <span class="info-value">普货件</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">对应业务大类：</span>
            <span class="info-value">手续费补贴</span>
          </div>
          <div class="info-item">
            <span class="info-label">业务类型：</span>
            <span class="info-value">手续费补贴</span>
          </div>
        </div>
      </div>

      <!-- 金额信息区域 -->
      <div class="dialog-section">
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">业务金额：</span>
            <span class="info-value">10000.00</span>
          </div>
          <div class="info-item">
            <span class="info-label">调整金额：</span>
            <span class="info-value">-1000.00</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">扣印花金额：</span>
            <span class="info-value">1000.00</span>
          </div>
          <div class="info-item">
            <span class="info-label">已开票金额：</span>
            <span class="info-value">1000.00</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="info-label">已回款金额：</span>
            <span class="info-value">1000.00</span>
          </div>
          <div class="info-item">
            <span class="info-label">可拆账金额：</span>
            <span class="info-value red-text">7000.00</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item" style="flex: 2;">
            <span class="info-label">回退原因：</span>
            <span class="info-value red-text">不允许拆账给异地邮政</span>
          </div>
        </div>

        <!-- 拆账明细区域 -->
        <div class="dialog-section">
          <div style="margin-bottom: 10px; font-weight: bold;">拆账明细</div>
          <div class="info-row">
            <div class="info-item">
              <span class="info-label">拆账金额：</span>
              <span class="info-value">7000.00；</span>
            </div>
            <div class="info-item">
              <span class="info-label">拆账金额：</span>
              <span class="info-value">3000.00</span>
            </div>
          </div>

          <table class="layui-table" style="margin-top: 15px;">
            <thead>
              <tr>
                <th style="width: 80px;">序号</th>
                <th>NC客商名称</th>
                <th style="width: 120px;">NC编号</th>
                <th style="width: 120px;">拆账金额</th>
                <th style="width: 80px;">操作</th>
              </tr>
            </thead>
            <tbody id="resplitTableBody">
              <!-- 重新拆账明细数据将通过接口动态填充 -->
            </tbody>
          </table>

          <div class="add-row" style="margin-top: 10px;">
            <a href="javascript:;" style="color: #1E9FFF;">+新增</a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script>
    layui.use(['layer', 'form'], function () {
      var layer = layui.layer;
      var form = layui.form;

      // 全局数据存储
      var globalData = {
        detailInfo: null,
        ncCustomerList: []
      };

      // 获取详情数据
      function getDetailInfo (params) {
        return new Promise(function (resolve, reject) {
          // 模拟数据（实际项目中替换为真实接口）
          var mockData = {
            ncCustomerName: '中国邮政集团有限公司福州市分公司',
            ncCode: 'N001616',
            businessMonth: '202503',
            productType: '普货件',
            businessCategory: '手续费补贴',
            businessType: '手续费补贴',
            businessAmount: '10000.00',
            adjustAmount: '-1000.00',
            stampAmount: '1000.00',
            invoicedAmount: '1000.00',
            receivedAmount: '1000.00',
            splitableAmount: '7000.00'
          };

          // 模拟异步请求
          setTimeout(function () {
            globalData.detailInfo = mockData;
            resolve(mockData);
          }, 100);
        });
      }

      // 获取NC客商列表
      function getNcCustomerList (ncName) {
        return new Promise(function (resolve, reject) {
          $.ajax({
            url: '/web_posm/feeBusinessShipment/getNcCustomerList',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
              ncName: ncName || ''
            }),
            success: function (res) {
              if (res.code === 200) {
                globalData.ncCustomerList = res.data || [];
                resolve(res.data || []);
              } else {
                layer.msg('获取NC客户数据失败: ' + res.msg, { icon: 2 });
                reject(res);
              }
            },
            error: function (xhr, status, error) {
              layer.msg('获取NC客户数据失败', { icon: 2 });
              reject(error);
            }
          });
        });
      }

      // 拆账申请弹窗
      function openApplyDialog (rowData) {
        // 从rowData中获取shipmentId
        var shipmentId = rowData && rowData.shipmentId ? rowData.shipmentId : '';
        if (!shipmentId) {
          layer.msg('缺少流水号参数，无法打开拆账申请', { icon: 2 });
          return;
        }

        // 显示加载提示
        var loadingIndex = layer.load(1, { shade: [0.1, '#fff'] });

        // 并行获取详情数据和NC客商列表
        Promise.all([
          getDetailInfo(rowData || {}),
          getNcCustomerList()
        ]).then(function (results) {
          var detailData = results[0];
          var customerList = results[1];

          // 关闭加载提示
          layer.close(loadingIndex);

          // 打开弹窗，传递shipmentId
          openApplyDialogWithData(detailData, customerList, shipmentId);
        }).catch(function (error) {
          // 关闭加载提示
          layer.close(loadingIndex);

          // 即使请求失败也要打开弹窗，使用默认数据
          var defaultDetailData = {
            ncCustomerName: '',
            ncCode: '',
            businessMonth: '',
            productType: '',
            businessCategory: '',
            businessType: '',
            businessAmount: '',
            adjustAmount: '',
            stampAmount: '',
            invoicedAmount: '',
            receivedAmount: '',
            splitableAmount: ''
          };

          // 显示错误提示但仍然打开弹窗
          layer.msg('获取数据失败，请手动填写', { icon: 2 });
          openApplyDialogWithData(defaultDetailData, [], shipmentId);
        });
      }

      // 打开拆账申请弹窗并填充数据
      function openApplyDialogWithData (detailData, customerList, shipmentId) {
        var dialogIndex = layer.open({
          type: 1,
          title: '拆账申请',
          area: ['800px', '800px'],
          content: $('#applyDialog').html(),
          btn: ['取消', '确认'],
          success: function (layero, index) {
            // 存储shipmentId到弹窗数据中
            layero.data('shipmentId', shipmentId);

            // 填充基本信息和金额信息
            fillDialogData(layero, detailData);
            // 填充NC客商下拉选项
            fillNcCustomerOptions(layero, customerList);
            // 绑定事件
            bindApplyDialogEvents(layero);
            // 渲染表单
            form.render();
          },
          btn2: function (index, layero) {
            // 点击确认按钮
            submitApplyForm(layero, index);
            return false; // 阻止默认关闭
          }
        });
      }

      // 提交拆账申请表单
      function submitApplyForm (layero, dialogIndex) {
        // 获取shipmentId
        var shipmentId = layero.data('shipmentId');
        if (!shipmentId) {
          layer.msg('缺少流水号参数', { icon: 2 });
          return;
        }

        // 收集表单数据
        var formData = collectApplyFormData(layero);
        if (!formData) {
          return; // 验证失败
        }

        // 构建请求参数
        var requestData = {
          shipmentId: shipmentId,
          userId: '', // 先传空
          infos: formData.infos
        };

        // 显示提交中状态
        var loadingIndex = layer.load(1, { shade: [0.3, '#fff'] });

        // 调用接口
        $.ajax({
          url: '/web_posm/feeBusinessShipment/applyBreakDownFee',
          type: 'POST',
          contentType: 'application/json',
          data: JSON.stringify(requestData),
          success: function (res) {
            layer.close(loadingIndex);
            if (res.code === 200) {
              layer.msg('拆账申请提交成功', { icon: 1 });
              layer.close(dialogIndex); // 关闭弹窗
              // 可以在这里刷新列表或执行其他操作
            } else {
              layer.msg('提交失败: ' + res.msg, { icon: 2 });
            }
          },
          error: function (xhr, status, error) {
            layer.close(loadingIndex);
            layer.msg('提交失败，请重试', { icon: 2 });
          }
        });
      }

      // 收集拆账申请表单数据
      function collectApplyFormData (layero) {
        var infos = [];
        var hasError = false;

        // 遍历所有拆账明细行
        layero.find('#applyTableBody tr').each(function (index) {
          var row = $(this);
          var rowNum = index + 1;

          var ncNameInput = row.find('input[name="ncName' + rowNum + '"]');
          var ncCodeInput = row.find('input[name="ncCode' + rowNum + '"]');
          var amountInput = row.find('input[name="amount' + rowNum + '"]');

          var ncName = ncNameInput.val().trim();
          var ncCode = ncCodeInput.val().trim();
          var amount = amountInput.val().trim();

          // 验证必填字段
          if (!ncName) {
            layer.msg('第' + rowNum + '行：请选择NC客商', { icon: 2 });
            ncNameInput.focus();
            hasError = true;
            return false;
          }

          // if (!ncCode) {
          //   layer.msg('第' + rowNum + '行：NC编号不能为空', { icon: 2 });
          //   hasError = true;
          //   return false;
          // }

          if (!amount) {
            layer.msg('第' + rowNum + '行：请输入拆账金额', { icon: 2 });
            amountInput.focus();
            hasError = true;
            return false;
          }

          // 验证金额格式
          if (isNaN(amount) || parseFloat(amount) <= 0) {
            layer.msg('第' + rowNum + '行：请输入有效的拆账金额', { icon: 2 });
            amountInput.focus();
            hasError = true;
            return false;
          }

          // 添加到数据数组
          infos.push({
            shipmentId: layero.data('shipmentId'),
            seq: rowNum.toString(),
            ncNo: ncCode,
            ncName: ncName,
            breakDownMoney: amount
          });
        });

        if (hasError) {
          return null;
        }

        if (infos.length === 0) {
          layer.msg('请至少添加一条拆账明细', { icon: 2 });
          return null;
        }

        return { infos: infos };
      }

      // 获取拆账明细数据
      function getBreakDownDetail (shipmentId) {
        return new Promise(function (resolve, reject) {
          $.ajax({
            url: '/web_posm/feeBusinessShipment/detailBreakDownFee',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
              shipmentId: shipmentId
            }),
            success: function (res) {
              if (res.code === 200) {
                resolve(res.data || []);
              } else {
                layer.msg('获取拆账明细失败: ' + res.msg, { icon: 2 });
                reject(res);
              }
            },
            error: function (xhr, status, error) {
              layer.msg('获取拆账明细失败', { icon: 2 });
              reject(error);
            }
          });
        });
      }

      // 拆账审核弹窗
      function openAuditDialog (rowData) {
        // 从rowData中获取shipmentId
        var shipmentId = rowData && rowData.shipmentId ? rowData.shipmentId : '';
        if (!shipmentId) {
          layer.msg('缺少流水号参数，无法打开拆账审核', { icon: 2 });
          return;
        }

        // 显示加载提示
        var loadingIndex = layer.load(1, { shade: [0.1, '#fff'] });

        // 并行获取详情数据和拆账明细
        Promise.all([
          getDetailInfo(rowData || {}),
          getBreakDownDetail(shipmentId)
        ]).then(function (results) {
          var detailData = results[0];
          var breakDownList = results[1];

          // 关闭加载提示
          layer.close(loadingIndex);

          // 打开弹窗，传递shipmentId和明细数据
          openAuditDialogWithData(detailData, breakDownList, shipmentId);
        }).catch(function (error) {
          // 关闭加载提示
          layer.close(loadingIndex);

          // 即使请求失败也要打开弹窗，使用默认数据
          var defaultDetailData = {
            ncCustomerName: '',
            ncCode: '',
            businessMonth: '',
            productType: '',
            businessCategory: '',
            businessType: '',
            businessAmount: '',
            adjustAmount: '',
            stampAmount: '',
            invoicedAmount: '',
            receivedAmount: '',
            splitableAmount: ''
          };

          // 显示错误提示但仍然打开弹窗
          layer.msg('获取数据失败，请手动填写', { icon: 2 });
          openAuditDialogWithData(defaultDetailData, [], shipmentId);
        });
      }

      // 打开拆账审核弹窗并填充数据
      function openAuditDialogWithData (detailData, breakDownList, shipmentId) {
        var dialogIndex = layer.open({
          type: 1,
          title: '拆账审核',
          area: ['800px', '800px'],
          content: $('#auditDialog').html(),
          btn: ['取消', '确认'],
          success: function (layero, index) {
            // 存储shipmentId到弹窗数据中
            layero.data('shipmentId', shipmentId);

            // 填充基本信息和金额信息
            fillDialogData(layero, detailData);

            // 填充拆账明细表格
            fillBreakDownTable(layero, breakDownList);

            // 渲染表单
            form.render();
          },
          btn2: function (index, layero) {
            // 点击确认按钮
            submitAuditForm(layero, index);
            return false; // 阻止默认关闭
          }
        });
      }

      // 通用的弹窗数据填充函数
      function fillDialogData (layero, detailData) {
        // 填充基本信息
        layero.find('input[name="ncCustomerName"]').val(detailData.ncCustomerName || '');
        layero.find('input[name="ncCode"]').val(detailData.ncCode || '');
        layero.find('input[name="businessMonth"]').val(detailData.businessMonth || '');
        layero.find('input[name="productType"]').val(detailData.productType || '');
        layero.find('input[name="businessCategory"]').val(detailData.businessCategory || '');
        layero.find('input[name="businessType"]').val(detailData.businessType || '');

        // 填充金额信息
        layero.find('input[name="businessAmount"]').val(detailData.businessAmount || '');
        layero.find('input[name="adjustAmount"]').val(detailData.adjustAmount || '');
        layero.find('input[name="stampAmount"]').val(detailData.stampAmount || '');
        layero.find('input[name="invoicedAmount"]').val(detailData.invoicedAmount || '');
        layero.find('input[name="receivedAmount"]').val(detailData.receivedAmount || '');
        layero.find('input[name="splitableAmount"]').val(detailData.splitableAmount || '');

        // 同时填充显示用的.info-value元素（用于申请弹窗）
        layero.find('.info-value').each(function () {
          var $this = $(this);
          var text = $this.text().trim();

          // 根据当前文本内容判断需要填充的字段
          if (text === '中国邮政集团有限公司福州市分公司' || text === '') {
            if ($this.closest('.info-item').find('.info-label').text().includes('NC客商名称')) {
              $this.text(detailData.ncCustomerName || '');
            }
          }
          if (text === 'N001616' || text === '') {
            if ($this.closest('.info-item').find('.info-label').text().includes('NC编号')) {
              $this.text(detailData.ncCode || '');
            }
          }
          if (text === '202503' || text === '') {
            if ($this.closest('.info-item').find('.info-label').text().includes('业务月份')) {
              $this.text(detailData.businessMonth || '');
            }
          }
          if (text === '普货件' || text === '') {
            if ($this.closest('.info-item').find('.info-label').text().includes('产品类型')) {
              $this.text(detailData.productType || '');
            }
          }
          if (text === '手续费补贴' || text === '') {
            if ($this.closest('.info-item').find('.info-label').text().includes('对应业务大类')) {
              $this.text(detailData.businessCategory || '');
            } else if ($this.closest('.info-item').find('.info-label').text().includes('业务类型')) {
              $this.text(detailData.businessType || '');
            }
          }
          if (text === '10000.00' || text === '') {
            if ($this.closest('.info-item').find('.info-label').text().includes('业务金额')) {
              $this.text(detailData.businessAmount || '');
            }
          }
          if (text === '-1000.00' || text === '') {
            if ($this.closest('.info-item').find('.info-label').text().includes('调整金额')) {
              $this.text(detailData.adjustAmount || '');
            }
          }
          if (text === '1000.00' || text === '') {
            if ($this.closest('.info-item').find('.info-label').text().includes('扣印花金额')) {
              $this.text(detailData.stampAmount || '');
            } else if ($this.closest('.info-item').find('.info-label').text().includes('已开票金额')) {
              $this.text(detailData.invoicedAmount || '');
            } else if ($this.closest('.info-item').find('.info-label').text().includes('已回款金额')) {
              $this.text(detailData.receivedAmount || '');
            }
          }
          if (text === '7000.00' || text === '') {
            if ($this.closest('.info-item').find('.info-label').text().includes('可拆账金额')) {
              $this.text(detailData.splitableAmount || '');
            }
          }
        });
      }

      // 填充拆账明细表格
      function fillBreakDownTable (layero, breakDownList) {
        var tbody = layero.find('#auditTableBody');
        tbody.empty();

        if (breakDownList && breakDownList.length > 0) {
          for (var i = 0; i < breakDownList.length; i++) {
            var item = breakDownList[i];
            var row = '<tr>' +
              '<td>' + (i + 1) + '</td>' +
              '<td>' + (item.ncName || '') + '</td>' +
              '<td>' + (item.ncNo || '') + '</td>' +
              '<td>' + (item.breakDownMoney || '') + '</td>' +
              '</tr>';
            tbody.append(row);
          }
        } else {
          tbody.append('<tr><td colspan="4" style="text-align: center; color: #999;">暂无拆账明细</td></tr>');
        }
      }

      // 提交审核表单
      function submitAuditForm (layero, dialogIndex) {
        // 获取shipmentId
        var shipmentId = layero.data('shipmentId');
        if (!shipmentId) {
          layer.msg('缺少流水号参数', { icon: 2 });
          return;
        }

        // 获取审核结果
        var auditResult = layero.find('select[name="auditResult"]').val();
        if (!auditResult) {
          layer.msg('请选择审核结果', { icon: 2 });
          return;
        }

        // 获取审核原因
        var auditReason = layero.find('textarea[name="auditReason"]').val().trim();
        if (!auditReason) {
          layer.msg('请输入审核原因', { icon: 2 });
          return;
        }

        // 构建请求参数
        var requestData = {
          shipmentId: shipmentId,
          userId: '', // 先传空
          returnReason: auditReason,
          auditStatus: auditResult
        };

        // 显示提交中状态
        var loadingIndex = layer.load(1, { shade: [0.3, '#fff'] });

        // 调用接口
        $.ajax({
          url: '/web_posm/feeBusinessShipment/auditBreakDownFee',
          type: 'POST',
          contentType: 'application/json',
          data: JSON.stringify(requestData),
          success: function (res) {
            layer.close(loadingIndex);
            if (res.code === 200) {
              layer.msg('审核提交成功', { icon: 1 });
              layer.close(dialogIndex); // 关闭弹窗
              // 可以在这里刷新列表或执行其他操作
            } else {
              layer.msg('审核失败: ' + res.msg, { icon: 2 });
            }
          },
          error: function (xhr, status, error) {
            layer.close(loadingIndex);
            layer.msg('审核失败，请重试', { icon: 2 });
          }
        });
      }

      // 重新拆账弹窗
      function openResplitDialog (rowData) {
        // 从rowData中获取shipmentId
        var shipmentId = rowData && rowData.shipmentId ? rowData.shipmentId : '';
        if (!shipmentId) {
          layer.msg('缺少流水号参数，无法打开重新拆账', { icon: 2 });
          return;
        }

        // 显示加载提示
        var loadingIndex = layer.load(1, { shade: [0.1, '#fff'] });

        // 并行获取详情数据、NC客商列表和拆账明细
        Promise.all([
          getDetailInfo(rowData || {}),
          getNcCustomerList(),
          getBreakDownDetail(shipmentId)
        ]).then(function (results) {
          var detailData = results[0];
          var customerList = results[1];
          var breakDownList = results[2];

          // 关闭加载提示
          layer.close(loadingIndex);

          // 打开弹窗，传递shipmentId和明细数据
          openResplitDialogWithData(detailData, customerList, breakDownList, shipmentId);
        }).catch(function (error) {
          // 关闭加载提示
          layer.close(loadingIndex);

          // 即使请求失败也要打开弹窗，使用默认数据
          var defaultDetailData = {
            ncCustomerName: '',
            ncCode: '',
            businessMonth: '',
            productType: '',
            businessCategory: '',
            businessType: '',
            businessAmount: '',
            adjustAmount: '',
            stampAmount: '',
            invoicedAmount: '',
            receivedAmount: '',
            splitableAmount: ''
          };

          // 显示错误提示但仍然打开弹窗
          layer.msg('获取数据失败，请手动填写', { icon: 2 });
          openResplitDialogWithData(defaultDetailData, [], [], shipmentId);
        });
      }

      // 打开重新拆账弹窗并填充数据
      function openResplitDialogWithData (detailData, customerList, breakDownList, shipmentId) {
        var dialogIndex = layer.open({
          type: 1,
          title: '重新拆账',
          area: ['800px', '800px'],
          content: $('#resplitDialog').html(),
          btn: ['取消', '确认'],
          success: function (layero, index) {
            // 存储shipmentId到弹窗数据中
            layero.data('shipmentId', shipmentId);

            // 填充基本信息和金额信息
            fillDialogData(layero, detailData);

            // 填充NC客商下拉选项
            fillNcCustomerOptions(layero, customerList);

            // 填充拆账明细表格（可编辑）
            fillResplitTable(layero, breakDownList);

            // 绑定事件
            bindResplitDialogEvents(layero);

            // 渲染表单
            form.render();
          },
          btn2: function (index, layero) {
            // 点击确认按钮
            submitResplitForm(layero, index);
            return false; // 阻止默认关闭
          }
        });
      }



      // 填充重新拆账明细表格（可编辑）
      function fillResplitTable (layero, breakDownList) {
        var tbody = layero.find('#resplitTableBody');
        tbody.empty();

        if (breakDownList && breakDownList.length > 0) {
          for (var i = 0; i < breakDownList.length; i++) {
            var item = breakDownList[i];
            var rowNum = i + 1;

            var row = '<tr>' +
              '<td>' + rowNum + '</td>' +
              '<td>' +
              '<div class="nc-customer-container" data-row="' + rowNum + '">' +
              '<input type="text" class="layui-input nc-customer-input" name="ncName' + rowNum + '" placeholder="请输入NC客商名称搜索" autocomplete="off" data-row="' + rowNum + '" value="' + (item.ncName || '') + '">' +
              '<input type="hidden" name="ncNameValue' + rowNum + '" value="' + (item.ncNo || '') + '">' +
              '<div class="nc-dropdown" style="display: none;"></div>' +
              '</div>' +
              '</td>' +
              '<td>' +
              '<input type="text" class="layui-input" name="ncCode' + rowNum + '" readonly placeholder="NC编号" value="' + (item.ncNo || '') + '">' +
              '</td>' +
              '<td>' +
              '<input type="text" class="layui-input" name="amount' + rowNum + '" placeholder="请输入拆账金额" value="' + (item.breakDownMoney || '') + '">' +
              '</td>' +
              '<td>' +
              '<button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete-row">删除</button>' +
              '</td>' +
              '</tr>';
            tbody.append(row);
          }
        } else {
          // 如果没有明细，添加一个空行
          addNewResplitRow(layero);
        }
      }

      // 绑定重新拆账弹窗事件
      function bindResplitDialogEvents (layero) {
        var searchTimer = null;

        // 新增按钮事件
        layero.find('.add-row a').off('click').on('click', function (e) {
          e.preventDefault();
          addNewResplitRow(layero);
        });

        // 删除按钮事件
        layero.off('click', '.delete-row').on('click', '.delete-row', function (e) {
          e.preventDefault();
          deleteTableRow(this, layero);
        });

        // NC客商搜索输入事件
        layero.off('input', '.nc-customer-input').on('input', '.nc-customer-input', function () {
          var input = $(this);
          var searchValue = input.val().trim();
          var rowNum = input.data('row');

          // 清空隐藏字段和NC编号
          layero.find('input[name="ncNameValue' + rowNum + '"]').val('');
          layero.find('input[name="ncCode' + rowNum + '"]').val('');

          // 清除之前的定时器
          if (searchTimer) {
            clearTimeout(searchTimer);
          }

          // 延迟搜索，避免频繁请求
          searchTimer = setTimeout(function () {
            if (searchValue.length >= 1) {
              // 显示加载状态
              showLoadingDropdown(input);

              // 调用搜索接口
              getNcCustomerList(searchValue).then(function (customerList) {
                showSearchDropdown(input, customerList);
              }).catch(function () {
                showSearchDropdown(input, []);
              });
            } else {
              hideSearchDropdown(input);
            }
          }, 300);
        });

        // NC客商选择事件
        layero.off('click', '.nc-dropdown-item').on('click', '.nc-dropdown-item', function () {
          var item = $(this);
          var ncCode = item.data('code');
          var ncName = item.data('name');

          if (ncCode && ncName) {
            var container = item.closest('.nc-customer-container');
            var rowNum = container.data('row');
            var input = container.find('.nc-customer-input');

            // 填入选中的客商名称
            input.val(ncName);

            // 存储选中的值到隐藏字段
            layero.find('input[name="ncNameValue' + rowNum + '"]').val(ncCode);

            // 自动填入NC编号
            layero.find('input[name="ncCode' + rowNum + '"]').val(ncCode);

            // 隐藏下拉框
            hideSearchDropdown(input);
          }
        });

        // 点击其他地方隐藏下拉框
        $(document).off('click.ncResplitSearch').on('click.ncResplitSearch', function (e) {
          if (!$(e.target).closest('.nc-customer-container').length) {
            layero.find('.nc-dropdown').hide();
          }
        });

        // 输入框失去焦点时延迟隐藏下拉框
        layero.off('blur', '.nc-customer-input').on('blur', '.nc-customer-input', function () {
          var input = $(this);
          setTimeout(function () {
            hideSearchDropdown(input);
          }, 200);
        });
      }

      // 添加新的重新拆账行
      function addNewResplitRow (layero) {
        var tbody = layero.find('#resplitTableBody');
        var rowCount = tbody.find('tr').length + 1;

        var newRow = '<tr>' +
          '<td>' + rowCount + '</td>' +
          '<td>' +
          '<div class="nc-customer-container" data-row="' + rowCount + '">' +
          '<input type="text" class="layui-input nc-customer-input" name="ncName' + rowCount + '" placeholder="请输入NC客商名称搜索" autocomplete="off" data-row="' + rowCount + '">' +
          '<input type="hidden" name="ncNameValue' + rowCount + '" value="">' +
          '<div class="nc-dropdown" style="display: none;"></div>' +
          '</div>' +
          '</td>' +
          '<td>' +
          '<input type="text" class="layui-input" name="ncCode' + rowCount + '" readonly placeholder="NC编号">' +
          '</td>' +
          '<td>' +
          '<input type="text" class="layui-input" name="amount' + rowCount + '" placeholder="请输入拆账金额">' +
          '</td>' +
          '<td>' +
          '<button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete-row">删除</button>' +
          '</td>' +
          '</tr>';
        tbody.append(newRow);

        // 新增行后需要重新绑定事件（只绑定新增行的事件）
        bindNewResplitRowSearchEvents(layero, rowCount);
      }

      // 绑定新增重新拆账行的搜索事件
      function bindNewResplitRowSearchEvents (layero, rowNum) {
        var newInput = layero.find('input[name="ncName' + rowNum + '"]');
        bindNcCustomerSearchEvents(newInput, layero);
      }

      // 提交重新拆账表单
      function submitResplitForm (layero, dialogIndex) {
        // 获取shipmentId
        var shipmentId = layero.data('shipmentId');
        if (!shipmentId) {
          layer.msg('缺少流水号参数', { icon: 2 });
          return;
        }

        // 收集表单数据（与申请弹窗相同的逻辑）
        var formData = collectResplitFormData(layero);
        if (!formData) {
          return; // 验证失败
        }

        // 构建请求参数（与申请弹窗相同）
        var requestData = {
          shipmentId: shipmentId,
          userId: '', // 先传空
          infos: formData.infos
        };

        // 显示提交中状态
        var loadingIndex = layer.load(1, { shade: [0.3, '#fff'] });

        // 调用接口（与申请弹窗相同的接口）
        $.ajax({
          url: '/web_posm/feeBusinessShipment/applyBreakDownFee',
          type: 'POST',
          contentType: 'application/json',
          data: JSON.stringify(requestData),
          success: function (res) {
            layer.close(loadingIndex);
            if (res.code === 200) {
              layer.msg('重新拆账申请提交成功', { icon: 1 });
              layer.close(dialogIndex); // 关闭弹窗
              // 可以在这里刷新列表或执行其他操作
            } else {
              layer.msg('提交失败: ' + res.msg, { icon: 2 });
            }
          },
          error: function (xhr, status, error) {
            layer.close(loadingIndex);
            layer.msg('提交失败，请重试', { icon: 2 });
          }
        });
      }

      // 收集重新拆账表单数据
      function collectResplitFormData (layero) {
        var infos = [];
        var hasError = false;

        // 遍历所有拆账明细行
        layero.find('#resplitTableBody tr').each(function (index) {
          var row = $(this);
          var rowNum = index + 1;

          var ncNameInput = row.find('input[name="ncName' + rowNum + '"]');
          var ncCodeInput = row.find('input[name="ncCode' + rowNum + '"]');
          var amountInput = row.find('input[name="amount' + rowNum + '"]');

          var ncName = ncNameInput.val().trim();
          var ncCode = ncCodeInput.val().trim();
          var amount = amountInput.val().trim();

          // 验证必填字段
          if (!ncName) {
            layer.msg('第' + rowNum + '行：请选择NC客商', { icon: 2 });
            ncNameInput.focus();
            hasError = true;
            return false;
          }

          if (!ncCode) {
            layer.msg('第' + rowNum + '行：NC编号不能为空', { icon: 2 });
            hasError = true;
            return false;
          }

          if (!amount) {
            layer.msg('第' + rowNum + '行：请输入拆账金额', { icon: 2 });
            amountInput.focus();
            hasError = true;
            return false;
          }

          // 验证金额格式
          if (isNaN(amount) || parseFloat(amount) <= 0) {
            layer.msg('第' + rowNum + '行：请输入有效的拆账金额', { icon: 2 });
            amountInput.focus();
            hasError = true;
            return false;
          }

          // 添加到数据数组
          infos.push({
            shipmentId: layero.data('shipmentId'),
            seq: rowNum.toString(),
            ncNo: ncCode,
            ncName: ncName,
            breakDownMoney: amount
          });
        });

        if (hasError) {
          return null;
        }

        if (infos.length === 0) {
          layer.msg('请至少添加一条拆账明细', { icon: 2 });
          return null;
        }

        return { infos: infos };
      }



      // 填充NC客商下拉选项（初始化时不填充，等待用户搜索）
      function fillNcCustomerOptions (layero, customerList) {
        // 存储客商列表供搜索使用
        globalData.ncCustomerList = customerList || [];
      }

      // 显示搜索结果下拉框
      function showSearchDropdown (inputElement, customerList) {
        var container = inputElement.closest('.nc-customer-container');
        var dropdown = container.find('.nc-dropdown');

        dropdown.empty();

        // 添加调试信息
        console.log('showSearchDropdown called with:', customerList);

        if (customerList && customerList.length > 0) {
          for (var i = 0; i < customerList.length; i++) {
            var customer = customerList[i];

            // 兼容不同的字段名
            var ncCode = customer.ncCode || customer.ncNo || customer.code || '';
            var ncName = customer.ncCustomerName || customer.ncName || customer.name || '';

            console.log('Customer item:', customer, 'ncCode:', ncCode, 'ncName:', ncName);

            if (ncName) {
              var item = $('<div class="nc-dropdown-item" data-code="' + ncCode + '" data-name="' + ncName + '">' +
                ncName + (ncCode ? ' (' + ncCode + ')' : '') +
                '</div>');
              dropdown.append(item);
            }
          }

          // 检查是否有有效的选项被添加
          if (dropdown.find('.nc-dropdown-item').length > 0) {
            dropdown.show();
          } else {
            dropdown.append('<div class="nc-dropdown-item no-data">暂无匹配的客商</div>');
            dropdown.show();
          }
        } else {
          console.log('No customer data or empty array');
          dropdown.append('<div class="nc-dropdown-item no-data">暂无匹配的客商</div>');
          dropdown.show();
        }
      }

      // 隐藏搜索结果下拉框
      function hideSearchDropdown (inputElement) {
        var container = inputElement.closest('.nc-customer-container');
        var dropdown = container.find('.nc-dropdown');
        dropdown.hide();
      }

      // 显示加载状态
      function showLoadingDropdown (inputElement) {
        var container = inputElement.closest('.nc-customer-container');
        var dropdown = container.find('.nc-dropdown');
        dropdown.empty();
        dropdown.append('<div class="nc-dropdown-item loading">搜索中...</div>');
        dropdown.show();
      }

      // 通用的NC客商搜索事件绑定函数
      function bindNcCustomerSearchEvents (inputElement, layero) {
        var searchTimer = null;
        var input = $(inputElement);
        var rowNum = input.data('row');

        // NC客商搜索输入事件
        input.off('input').on('input', function () {
          var searchValue = input.val().trim();

          // 清空隐藏字段和NC编号
          layero.find('input[name="ncNameValue' + rowNum + '"]').val('');
          layero.find('input[name="ncCode' + rowNum + '"]').val('');

          // 清除之前的定时器
          if (searchTimer) {
            clearTimeout(searchTimer);
          }

          // 延迟搜索，避免频繁请求
          searchTimer = setTimeout(function () {
            if (searchValue.length >= 1) {
              // 显示加载状态
              showLoadingDropdown(input);

              // 调用搜索接口
              getNcCustomerList(searchValue).then(function (customerList) {
                showSearchDropdown(input, customerList);
              }).catch(function () {
                showSearchDropdown(input, []);
              });
            } else {
              hideSearchDropdown(input);
            }
          }, 300);
        });

        // 输入框失去焦点时延迟隐藏下拉框
        input.off('blur').on('blur', function () {
          setTimeout(function () {
            hideSearchDropdown(input);
          }, 200);
        });
      }

      // 绑定拆账申请弹窗事件
      function bindApplyDialogEvents (layero) {
        var searchTimer = null;

        // 新增按钮事件
        layero.find('.add-row a').off('click').on('click', function (e) {
          e.preventDefault();
          addNewRow(layero);
        });

        // 删除按钮事件
        layero.off('click', '.delete-row').on('click', '.delete-row', function (e) {
          e.preventDefault();
          deleteTableRow(this, layero);
        });

        // NC客商搜索输入事件
        layero.off('input', '.nc-customer-input').on('input', '.nc-customer-input', function () {
          var input = $(this);
          var searchValue = input.val().trim();
          var rowNum = input.data('row');

          // 清空隐藏字段和NC编号
          layero.find('input[name="ncNameValue' + rowNum + '"]').val('');
          layero.find('input[name="ncCode' + rowNum + '"]').val('');

          // 清除之前的定时器
          if (searchTimer) {
            clearTimeout(searchTimer);
          }

          // 延迟搜索，避免频繁请求
          searchTimer = setTimeout(function () {
            if (searchValue.length >= 1) {
              // 显示加载状态
              showLoadingDropdown(input);

              // 调用搜索接口
              getNcCustomerList(searchValue).then(function (customerList) {
                showSearchDropdown(input, customerList);
              }).catch(function () {
                showSearchDropdown(input, []);
              });
            } else {
              hideSearchDropdown(input);
            }
          }, 300);
        });

        // NC客商选择事件
        layero.off('click', '.nc-dropdown-item').on('click', '.nc-dropdown-item', function () {
          var item = $(this);
          var ncCode = item.data('code');
          var ncName = item.data('name');

          if (ncCode && ncName) {
            var container = item.closest('.nc-customer-container');
            var rowNum = container.data('row');
            var input = container.find('.nc-customer-input');

            // 填入选中的客商名称
            input.val(ncName);

            // 存储选中的值到隐藏字段
            layero.find('input[name="ncNameValue' + rowNum + '"]').val(ncCode);

            // 自动填入NC编号
            layero.find('input[name="ncCode' + rowNum + '"]').val(ncCode);

            // 隐藏下拉框
            hideSearchDropdown(input);
          }
        });

        // 点击其他地方隐藏下拉框
        $(document).off('click.ncSearch').on('click.ncSearch', function (e) {
          if (!$(e.target).closest('.nc-customer-container').length) {
            layero.find('.nc-dropdown').hide();
          }
        });

        // 输入框失去焦点时延迟隐藏下拉框
        layero.off('blur', '.nc-customer-input').on('blur', '.nc-customer-input', function () {
          var input = $(this);
          setTimeout(function () {
            hideSearchDropdown(input);
          }, 200);
        });
      }



      // 添加新行
      function addNewRow (layero) {
        var tbody = layero.find('tbody');
        var rowCount = tbody.find('tr').length + 1;

        var newRow = '<tr>' +
          '<td>' + rowCount + '</td>' +
          '<td>' +
          '<div class="nc-customer-container" data-row="' + rowCount + '">' +
          '<input type="text" class="layui-input nc-customer-input" name="ncName' + rowCount + '" placeholder="请输入NC客商名称搜索" autocomplete="off" data-row="' + rowCount + '">' +
          '<input type="hidden" name="ncNameValue' + rowCount + '" value="">' +
          '<div class="nc-dropdown" style="display: none;"></div>' +
          '</div>' +
          '</td>' +
          '<td>' +
          '<input type="text" class="layui-input" name="ncCode' + rowCount + '" readonly placeholder="NC编号">' +
          '</td>' +
          '<td>' +
          '<input type="text" class="layui-input" name="amount' + rowCount + '" placeholder="请输入拆账金额">' +
          '</td>' +
          '<td>' +
          '<button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete-row">删除</button>' +
          '</td>' +
          '</tr>';
        tbody.append(newRow);

        // 新增行后需要重新绑定事件（只绑定新增行的事件）
        bindNewRowSearchEvents(layero, rowCount);
      }

      // 绑定新增行的搜索事件
      function bindNewRowSearchEvents (layero, rowNum) {
        var newInput = layero.find('input[name="ncName' + rowNum + '"]');
        bindNcCustomerSearchEvents(newInput, layero);
      }

      // 删除行
      function deleteTableRow (btn, layero) {
        var tbody = layero.find('tbody');
        var rows = tbody.find('tr');

        if (rows.length <= 1) {
          layer.msg('至少需要保留一条拆账明细记录', { icon: 2 });
          return;
        }

        $(btn).closest('tr').remove();

        // 重新编号
        tbody.find('tr').each(function (index) {
          $(this).find('td:first').text(index + 1);
        });
      }

      // 绑定主页面按钮事件
      document.getElementById('splitApply').addEventListener('click', function () {
        // 模拟从列表中获取的数据，实际项目中应该从列表行中获取
        var rowData = {
          shipmentId: '123123' // 这里应该是从列表中获取的真实流水号
        };
        openApplyDialog(rowData);
      });

      document.getElementById('splitAudit').addEventListener('click', function () {
        // 模拟从列表中获取的数据，实际项目中应该从列表行中获取
        var rowData = {
          shipmentId: '123123' // 这里应该是从列表中获取的真实流水号
        };
        openAuditDialog(rowData);
      });

      document.getElementById('reSplit').addEventListener('click', function () {
        // 模拟从列表中获取的数据，实际项目中应该从列表行中获取
        var rowData = {
          shipmentId: '123123' // 这里应该是从列表中获取的真实流水号
        };
        openResplitDialog(rowData);
      });
    });
  </script>
</body>

</html>